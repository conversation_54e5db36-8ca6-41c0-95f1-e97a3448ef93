import { Metada<PERSON> } from 'next';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Download, Trash2, Edit, Eye, Shield, AlertTriangle } from 'lucide-react';

export const metadata: Metadata = {
  title: 'GDPR Rights | Your SaaS App',
  description: 'Exercise your GDPR rights including data access, rectification, erasure, and portability.',
};

export default function GDPRRights() {
  return (
    <div className="min-h-screen bg-neutral-950 text-white">
      <div className="max-w-4xl mx-auto px-4 py-16">
        <div className="text-center mb-12">
          <Shield className="w-16 h-16 mx-auto mb-4 text-blue-400" />
          <h1 className="text-4xl font-bold mb-4">Your GDPR Rights</h1>
          <p className="text-neutral-400 text-lg">
            Exercise your data protection rights under the General Data Protection Regulation
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {/* Right to Access */}
          <Card className="bg-neutral-900 border-neutral-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="w-5 h-5 text-blue-400" />
                Right to Access
              </CardTitle>
              <CardDescription>
                Get a copy of all personal data we have about you
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-neutral-400 mb-4">
                Request a comprehensive report of your personal data, including how it's processed and shared.
              </p>
              <Button className="w-full" variant="outline">
                Request Data Access
              </Button>
            </CardContent>
          </Card>

          {/* Right to Data Portability */}
          <Card className="bg-neutral-900 border-neutral-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Download className="w-5 h-5 text-green-400" />
                Right to Data Portability
              </CardTitle>
              <CardDescription>
                Download your data in a machine-readable format
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-neutral-400 mb-4">
                Export your personal data in JSON format to transfer to another service.
              </p>
              <Button className="w-full" variant="outline">
                Export My Data
              </Button>
            </CardContent>
          </Card>

          {/* Right to Rectification */}
          <Card className="bg-neutral-900 border-neutral-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Edit className="w-5 h-5 text-yellow-400" />
                Right to Rectification
              </CardTitle>
              <CardDescription>
                Correct or update your personal information
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-neutral-400 mb-4">
                Update inaccurate or incomplete personal data in your account.
              </p>
              <Button className="w-full" variant="outline">
                Update My Information
              </Button>
            </CardContent>
          </Card>

          {/* Right to Erasure */}
          <Card className="bg-neutral-900 border-neutral-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Trash2 className="w-5 h-5 text-red-400" />
                Right to Erasure
              </CardTitle>
              <CardDescription>
                Request deletion of your personal data
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-neutral-400 mb-4">
                Permanently delete your account and associated personal data.
              </p>
              <Button className="w-full" variant="destructive">
                Delete My Account
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Additional Rights */}
        <div className="mt-12">
          <h2 className="text-2xl font-semibold mb-6">Additional Rights</h2>
          <div className="grid gap-4">
            <Card className="bg-neutral-900 border-neutral-800">
              <CardContent className="pt-6">
                <h3 className="font-semibold mb-2">Right to Object</h3>
                <p className="text-sm text-neutral-400 mb-4">
                  Object to processing of your personal data based on legitimate interests or for direct marketing.
                </p>
                <Button variant="ghost" size="sm">
                  Manage Objections
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-neutral-900 border-neutral-800">
              <CardContent className="pt-6">
                <h3 className="font-semibold mb-2">Right to Restrict Processing</h3>
                <p className="text-sm text-neutral-400 mb-4">
                  Limit how we process your personal data in certain circumstances.
                </p>
                <Button variant="ghost" size="sm">
                  Request Restrictions
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-neutral-900 border-neutral-800">
              <CardContent className="pt-6">
                <h3 className="font-semibold mb-2">Right to Withdraw Consent</h3>
                <p className="text-sm text-neutral-400 mb-4">
                  Withdraw your consent for data processing that requires your explicit consent.
                </p>
                <Button variant="ghost" size="sm">
                  Manage Consent
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Important Information */}
        <div className="mt-12">
          <Card className="bg-amber-950/20 border-amber-800/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-amber-400">
                <AlertTriangle className="w-5 h-5" />
                Important Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm">
                <li>• We will respond to your requests within 30 days</li>
                <li>• Some requests may require identity verification</li>
                <li>• Certain data may be retained for legal compliance</li>
                <li>• You can contact our Data Protection Officer at privacy@[yourdomain].com</li>
                <li>• You have the right to lodge a complaint with your data protection authority</li>
              </ul>
            </CardContent>
          </Card>
        </div>

        {/* Contact Information */}
        <div className="mt-12 text-center">
          <h2 className="text-xl font-semibold mb-4">Need Help?</h2>
          <p className="text-neutral-400 mb-4">
            If you have questions about your rights or need assistance, contact our Data Protection Officer:
          </p>
          <div className="space-y-2">
            <p>Email: <a href="mailto:privacy@[yourdomain].com" className="text-blue-400 hover:underline">privacy@[yourdomain].com</a></p>
            <p>Response time: Within 30 days</p>
          </div>
        </div>
      </div>
    </div>
  );
}